package com.knet.goods.model.dto.req;

import com.knet.common.base.BasePageRequest;
import com.knet.common.enums.ProductMark;
import com.knet.common.enums.SortByFileds;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/19 14:04
 * @description: 商品查询请求体
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductQueryRequest extends BasePageRequest {
    @Schema(description = "sku", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sku;

    @Schema(description = "品牌", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String brand;

    @Schema(description = "账号", requiredMode = Schema.RequiredMode.NOT_REQUIRED, hidden = true)
    private String account;

    /**
     * @see SortByFileds 排序字段
     */
    @Schema(description = "排序字段", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private SortByFileds sortBy;

    /**
     * @see ProductMark 商品标识
     */
    @Schema(description = "商品标识，HOT_SALE NEW COMMON 查询全部 不传值 ", example = "COMMON", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private ProductMark mark;

    @Schema(description = "sku列表", requiredMode = Schema.RequiredMode.NOT_REQUIRED, hidden = true)
    private List<String> skus;

    @Schema(description = "商品名称列表", requiredMode = Schema.RequiredMode.NOT_REQUIRED, hidden = true)
    private List<String> remarks;

    @Schema(description = "尺码筛选", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String spec;

    @Builder.Default
    @Schema(description = "商品数量最小值（需与maxTotal同时提供）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer minTotal = 0;

    @Builder.Default
    @Schema(description = "商品数量最大值（需与minTotal同时提供）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer maxTotal = 1000;

    @Builder.Default
    @Schema(description = "价格区间最小值（策略价格，美元，需与maxPrice同时提供）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String minPrice = "0.00";

    @Builder.Default
    @Schema(description = "价格区间最大值 10W（策略价格，美元，需与minPrice同时提供）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String maxPrice = "100000.00";
}
